import { z } from 'zod';
import { BaseAgent, type BaseAgentOptions, type ExtraAgentOptions } from './base';
import { createLogger } from '@src/background/log';
import { type AgentOutput } from '../types';
import { Actors, ExecutionState } from '../event/types';
import { DataExportService, type ExportOptions } from '../../services/dataExportService';

const logger = createLogger('ExporterAgent');

// Define Zod schema for exporter output
export const exporterOutputSchema = z.object({
  analysis: z.object({
    data_available: z.boolean(),
    data_count: z.number(),
    data_type: z.enum(['tabular', 'list', 'nested', 'mixed']),
    user_intent: z.enum(['export', 'chart', 'pdf', 'table', 'unknown']),
    recommended_action: z.string(),
  }),
  action: z.object({
    type: z.enum(['export', 'chart', 'pdf', 'table', 'error']),
    format: z.enum(['csv', 'json', 'excel', 'bar_chart', 'line_chart', 'pie_chart', 'report', 'interactive_table']),
    options: z.object({
      filename: z.string(),
      title: z.string().optional(),
      chart_type: z.enum(['bar', 'line', 'pie', 'doughnut', 'radar', 'scatter', 'heatmap', 'treemap', '3d']).optional(),
      library: z.enum(['chartjs', 'echarts', 'apexcharts']).optional(),
      include_metadata: z.boolean(),
    }),
    parameters: z.object({
      x_axis: z.string().optional(),
      y_axis: z.string().optional(),
      group_by: z.string().optional(),
      aggregation: z.enum(['sum', 'count', 'avg', 'max', 'min']).optional(),
      color_scheme: z.string().optional(),
      layout: z.enum(['vertical', 'horizontal', 'grid']).optional(),
    }),
  }),
  message: z.string(),
});

export type ExporterOutput = z.infer<typeof exporterOutputSchema>;

export interface ExportResult {
  success: boolean;
  content?: string;
  filename: string;
  mimeType?: string;
  downloadUrl?: string;
  size?: number;
  error?: string;
  chart_data?: any;
  pdf_data?: Uint8Array;
}

export class ExporterAgent extends BaseAgent<typeof exporterOutputSchema, ExporterOutput> {
  private exportService: DataExportService;
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];
  private isRecording = false;

  constructor(options: BaseAgentOptions, extraOptions?: Partial<ExtraAgentOptions>) {
    super(exporterOutputSchema, options, { ...extraOptions, id: 'exporter' });
    this.exportService = new DataExportService();
  }

  /**
   * Check if the user request contains export intent
   */
  shouldActivate(userMessage: string): boolean {
    const exportKeywords = [
      'export', 'download', 'save as', 'convert to', 'generate file',
      'csv', 'json', 'excel', 'xlsx', 'txt', 'text file', 'spreadsheet',
      'save data', 'export data', 'download data', 'chart', 'graph', 
      'visualize', 'plot', 'report', 'pdf', 'table', 'grid'
    ];

    const message = userMessage.toLowerCase();
    return exportKeywords.some(keyword => message.includes(keyword));
  }

  /**
   * Execute export process - Now with direct action bypass for simple tasks
   */
  async execute(): Promise<AgentOutput<ExporterOutput>> {
    try {
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.STEP_START, 'Analyzing export request...');

      const systemMessage = this.prompt.getSystemMessage();
      const userMessage = await this.prompt.getUserMessage(this.context);
      const inputMessages = [systemMessage, userMessage];

      const modelOutput = await this.invoke(inputMessages);
      if (!modelOutput) {
        throw new Error('Failed to get export analysis');
      }

      logger.info('Exporter analysis', JSON.stringify(modelOutput, null, 2));

      // Execute the determined action
      const result = await this.executeAction(modelOutput);

      if (result.success) {
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.STEP_OK, modelOutput.message);
      } else {
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.STEP_FAIL, result.error || 'Export failed');
      }

      return {
        id: this.id,
        result: modelOutput,
      };

    } catch (error) {
      const errorMsg = `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      logger.error('Export execution failed', error);
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.STEP_FAIL, errorMsg);

      return {
        id: this.id,
        error: errorMsg,
      };
    }
  }

  /**
   * Direct export data without AI analysis - for immediate action
   */
  async exportDataDirect(format: 'csv' | 'json' | 'excel', filename?: string): Promise<ExportResult> {
    try {
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_START, `Exporting data to ${format.toUpperCase()}...`);

      const extractedData = this.getExtractedData();
      if (!extractedData) {
        return {
          success: false,
          filename: 'no_data',
          error: 'No data available for export',
        };
      }

      const exportOptions = {
        format,
        filename: filename || `export_${Date.now()}.${format === 'excel' ? 'xlsx' : format}`,
        includeMetadata: true,
      };

      const result = await this.exportService.exportData(extractedData, exportOptions);
      
      if (result.success) {
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, `Exported ${extractedData.data.length} items to ${result.filename}`);
      } else {
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, result.error || 'Export failed');
      }

      return result;
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Export failed';
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);
      return {
        success: false,
        filename: 'export_error',
        error: errorMsg,
      };
    }
  }

  /**
   * Direct chart generation without AI analysis
   */
  async generateChartDirect(chartType: string, options: any): Promise<ExportResult> {
    try {
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_START, `Generating ${chartType} chart...`);

      const extractedData = this.getExtractedData();
      if (!extractedData) {
        return {
          success: false,
          filename: 'no_data',
          error: 'No data available for chart generation',
        };
      }

      const chartOptions = {
        chartType,
        title: options.title || `${chartType} Chart`,
        library: options.library || 'chartjs',
        filename: options.filename || `chart_${chartType}_${Date.now()}.html`,
        ...options
      };

      const result = await this.exportService.generateChart(extractedData.data, chartOptions);
      
      if (result.success) {
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, `Chart generated: ${chartOptions.filename}`);
        return {
          success: true,
          filename: chartOptions.filename,
          chart_data: result.chartHtml,
          mimeType: 'text/html',
        };
      } else {
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, result.error || 'Chart generation failed');
        return {
          success: false,
          filename: 'chart_error',
          error: result.error,
        };
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Chart generation failed';
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);
      return {
        success: false,
        filename: 'chart_error',
        error: errorMsg,
      };
    }
  }

  /**
   * Execute the determined action based on model output
   */
  private async executeAction(modelOutput: ExporterOutput): Promise<ExportResult> {
    const { action, analysis } = modelOutput;

    try {
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_START, `Executing ${action.type} action...`);

      switch (action.type) {
        case 'export':
          return await this.handleDataExport(action, analysis);
        
        case 'chart':
          return await this.handleChartGeneration(action, analysis);
        
        case 'pdf':
          return await this.handlePDFGeneration(action, analysis);
        
        case 'table':
          return await this.handleTableGeneration(action, analysis);
        
        case 'error':
          return {
            success: false,
            filename: 'error',
            error: modelOutput.message,
          };
        
        default:
          return {
            success: false,
            filename: 'unknown_action',
            error: `Unknown action type: ${action.type}`,
          };
      }
    } catch (error) {
      return {
        success: false,
        filename: 'execution_error',
        error: error instanceof Error ? error.message : 'Unknown execution error',
      };
    }
  }

  /**
   * Handle traditional data export (CSV, JSON, Excel)
   */
  private async handleDataExport(action: ExporterOutput['action'], _analysis: ExporterOutput['analysis']): Promise<ExportResult> {
    const extractedData = this.getExtractedData();
    if (!extractedData) {
      return {
        success: false,
        filename: 'no_data',
        error: 'No data available for export',
      };
    }

    const exportOptions: ExportOptions = {
      format: action.format as 'csv' | 'json' | 'excel',
      filename: action.options.filename,
      includeMetadata: action.options.include_metadata,
    };

    const result = await this.exportService.exportData(extractedData, exportOptions);
    this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, `Data exported to ${action.format.toUpperCase()}`);
    
    return {
      success: result.success,
      filename: result.filename,
      downloadUrl: result.downloadUrl,
      size: result.size,
      error: result.error,
    };
  }

  /**
   * Handle chart generation using available libraries
   */
  private async handleChartGeneration(action: ExporterOutput['action'], _analysis: ExporterOutput['analysis']): Promise<ExportResult> {
    const extractedData = this.getExtractedData();
    if (!extractedData) {
      return {
        success: false,
        filename: 'no_data',
        error: 'No data available for chart generation',
      };
    }

    try {
      // Load the appropriate chart library
      const chartLibrary = await this.loadChartLibrary(action.options.library || 'chartjs');
      
      // Process data for charting
      const chartData = this.processDataForChart(extractedData.data, action.parameters);
      
      // Generate chart configuration
      const chartConfig = this.generateChartConfig(chartData, action);
      
      // Create chart and export as image/HTML
      const chartResult = await this.createChart(chartLibrary, chartConfig, action);

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, `Chart generated using ${action.options.library}`);
      
      return {
        success: true,
        filename: action.options.filename,
        chart_data: chartResult,
        mimeType: 'text/html',
      };
    } catch (error) {
      return {
        success: false,
        filename: 'chart_error',
        error: error instanceof Error ? error.message : 'Chart generation failed',
      };
    }
  }

  /**
   * Handle PDF report generation
   */
  private async handlePDFGeneration(action: ExporterOutput['action'], analysis: ExporterOutput['analysis']): Promise<ExportResult> {
    try {
      // Load PDF library
      const pdfLib = await this.loadPDFLibrary();
      
      // Generate PDF document
      const pdfDoc = await this.createPDFReport(pdfLib, action, analysis);
      
      // Convert to bytes
      const pdfBytes = await pdfDoc.save();

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, 'PDF report generated');
      
      return {
        success: true,
        filename: action.options.filename,
        pdf_data: pdfBytes,
        mimeType: 'application/pdf',
        size: pdfBytes.length,
      };
    } catch (error) {
      return {
        success: false,
        filename: 'pdf_error',
        error: error instanceof Error ? error.message : 'PDF generation failed',
      };
    }
  }

  /**
   * Handle interactive table generation
   */
  private async handleTableGeneration(action: ExporterOutput['action'], _analysis: ExporterOutput['analysis']): Promise<ExportResult> {
    const extractedData = this.getExtractedData();
    if (!extractedData) {
      return {
        success: false,
        filename: 'no_data',
        error: 'No data available for table generation',
      };
    }

    try {
      // Load GridJS library
      const gridJS = await this.loadGridJSLibrary();
      
      // Generate interactive table HTML
      const tableHTML = this.generateInteractiveTable(gridJS, extractedData.data, action);

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, 'Interactive table generated');
      
      return {
        success: true,
        filename: action.options.filename,
        content: tableHTML,
        mimeType: 'text/html',
      };
    } catch (error) {
      return {
        success: false,
        filename: 'table_error',
        error: error instanceof Error ? error.message : 'Table generation failed',
      };
    }
  }

  /**
   * Get extracted data with flexible detection
   */
  private getExtractedData(): any | null {
    const extractedData = this.context.lastExtractionResult;
    
    if (!extractedData) return null;
    
    // Method 1: Standard format with .data property
    if (extractedData.data && Array.isArray(extractedData.data) && extractedData.data.length > 0) {
      return extractedData;
    }
    
    // Method 2: Direct array
    if (Array.isArray(extractedData) && extractedData.length > 0) {
      return {
        success: true,
        data: extractedData,
        metadata: {
          source_url: 'AI_GENERATED',
          extraction_timestamp: Date.now(),
          total_items: extractedData.length,
          extraction_method: 'direct_array',
          confidence_score: 0.8,
        },
      };
    }
    
    return null;
  }

  /**
   * Load chart library dynamically
   */
  private async loadChartLibrary(library: string): Promise<any> {
    const libraryPaths = {
      chartjs: 'node_modules/@lib/chart.min.js',
      echarts: 'node_modules/@lib/echarts.js',
      apexcharts: 'node_modules/@lib/apexcharts.min.js',
    };

    const path = libraryPaths[library as keyof typeof libraryPaths];
    if (!path) {
      throw new Error(`Unknown chart library: ${library}`);
    }

    try {
    const libraryPath = chrome.runtime.getURL(path);
      console.log(`[ExporterAgent] Loading chart library from: ${libraryPath}`);
      
    const response = await fetch(libraryPath);
      if (!response.ok) {
        throw new Error(`Failed to fetch chart library: ${response.status}`);
      }
      
    const libraryCode = await response.text();
    
      // Evaluate the library code in global context
    eval(libraryCode);
    
    // Return the global object based on library
    switch (library) {
      case 'chartjs':
          if (!(globalThis as any).Chart) {
            throw new Error('Chart.js library not found after loading');
          }
          console.log('[ExporterAgent] Chart.js loaded successfully');
        return (globalThis as any).Chart;
      case 'echarts':
          if (!(globalThis as any).echarts) {
            throw new Error('ECharts library not found after loading');
          }
          console.log('[ExporterAgent] ECharts loaded successfully');
        return (globalThis as any).echarts;
      case 'apexcharts':
          if (!(globalThis as any).ApexCharts) {
            throw new Error('ApexCharts library not found after loading');
          }
          console.log('[ExporterAgent] ApexCharts loaded successfully');
        return (globalThis as any).ApexCharts;
      default:
        throw new Error(`Failed to load library: ${library}`);
      }
    } catch (error) {
      logger.error('Failed to load chart library', error);
      console.error(`[ExporterAgent] Failed to load chart library ${library}:`, error);
      throw new Error(`Chart library ${library} not available: ${error}`);
    }
  }

  /**
   * Load PDF library dynamically
   */
  private async loadPDFLibrary(): Promise<any> {
    try {
    const pdfLibPath = chrome.runtime.getURL('node_modules/@lib/pdf-lib.min.js');
      console.log(`[ExporterAgent] Loading PDF library from: ${pdfLibPath}`);
      
    const response = await fetch(pdfLibPath);
      if (!response.ok) {
        throw new Error(`Failed to fetch PDF library: ${response.status}`);
      }
      
    const pdfLibCode = await response.text();
    
      // Evaluate the library code
    eval(pdfLibCode);
      
      // Check if PDFLib is now available
      if (!(globalThis as any).PDFLib) {
        throw new Error('PDF-lib library not found after loading');
      }
      
      console.log('[ExporterAgent] PDF-lib loaded successfully');
    return (globalThis as any).PDFLib;
    } catch (error) {
      logger.error('Failed to load PDF library', error);
      console.error('[ExporterAgent] Failed to load PDF library:', error);
      throw new Error(`PDF library not available: ${error}`);
    }
  }

  /**
   * Load GridJS library dynamically
   */
  private async loadGridJSLibrary(): Promise<any> {
    try {
    const gridJSPath = chrome.runtime.getURL('node_modules/@lib/gridjs.umd.js');
      console.log(`[ExporterAgent] Loading GridJS library from: ${gridJSPath}`);
      
    const response = await fetch(gridJSPath);
      if (!response.ok) {
        throw new Error(`Failed to fetch GridJS library: ${response.status}`);
      }
      
    const gridJSCode = await response.text();
    
      // Evaluate the library code
    eval(gridJSCode);
      
      // Check if gridjs is now available
      if (!(globalThis as any).gridjs) {
        throw new Error('GridJS library not found after loading');
      }
      
      console.log('[ExporterAgent] GridJS loaded successfully');
    return (globalThis as any).gridjs;
    } catch (error) {
      logger.error('Failed to load GridJS library', error);
      console.error('[ExporterAgent] Failed to load GridJS library:', error);
      throw new Error(`GridJS library not available: ${error}`);
    }
  }

  /**
   * Process data for chart generation
   */
  private processDataForChart(data: any[], parameters: ExporterOutput['action']['parameters']): any {
    try {
      console.log('[ExporterAgent] Processing data for chart:', { dataLength: data.length, parameters });
      
      // Extract labels and values based on parameters
      const xAxisField = parameters.x_axis || Object.keys(data[0] || {})[0];
      const yAxisField = parameters.y_axis || Object.keys(data[0] || {})[1];
      
      const labels = data.map((item, index) => 
        xAxisField ? (item[xAxisField] || `Item ${index + 1}`) : `Item ${index + 1}`
      );
      
      const values = data.map((item, index) => {
        if (yAxisField && item[yAxisField] !== undefined) {
          const value = item[yAxisField];
          return typeof value === 'number' ? value : parseFloat(value) || index + 1;
        }
        return index + 1;
      });
      
      console.log('[ExporterAgent] Chart data processed:', { labels: labels.slice(0, 3), values: values.slice(0, 3) });
      
      return { labels, values };
    } catch (error) {
      logger.error('Failed to process data for chart', error);
      console.error('[ExporterAgent] Failed to process data for chart:', error);
      throw error;
    }
  }

  /**
   * Generate chart configuration
   */
  private generateChartConfig(chartData: any, action: ExporterOutput['action']): any {
    try {
      const { labels, values } = chartData;
      const chartType = action.options.chart_type || 'bar';
      const title = action.options.title || 'Data Visualization';
      
      console.log('[ExporterAgent] Generating chart config:', { chartType, title, dataPoints: values.length });
      
      // Chart.js configuration
      if (action.options.library === 'chartjs') {
    return {
          type: chartType === 'doughnut' ? 'doughnut' : chartType,
          data: {
            labels,
            datasets: [{
              label: title,
              data: values,
              backgroundColor: this.getColorScheme(action.parameters.color_scheme || 'default'),
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1
            }]
          },
      options: {
        responsive: true,
        plugins: {
          title: {
            display: true,
                text: title,
                font: {
                  size: 16
                }
              },
              legend: {
                display: true,
                position: 'top'
              }
            },
            scales: chartType !== 'pie' && chartType !== 'doughnut' ? {
              y: {
                beginAtZero: true
              }
            } : undefined
          }
        };
      }
      
      // ECharts configuration
      if (action.options.library === 'echarts') {
        return {
          title: {
            text: title,
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: labels
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: title,
            type: chartType,
            data: values
          }]
        };
      }
      
      // ApexCharts configuration
      if (action.options.library === 'apexcharts') {
        return {
          series: [{
            name: title,
            data: values
          }],
          chart: {
            type: chartType,
            height: 350
          },
          title: {
            text: title,
            align: 'center'
          },
          xaxis: {
            categories: labels
          }
        };
      }
      
      throw new Error(`Unsupported chart library: ${action.options.library}`);
    } catch (error) {
      logger.error('Failed to generate chart config', error);
      console.error('[ExporterAgent] Failed to generate chart config:', error);
      throw error;
    }
  }

  /**
   * Create chart and return HTML
   */
  private async createChart(_chartLibrary: any, chartConfig: any, action: ExporterOutput['action']): Promise<string> {
    try {
      const title = action.options.title || 'Data Visualization';
      const library = action.options.library || 'chartjs';
      
      console.log('[ExporterAgent] Creating chart HTML:', { library, title });
      
      let chartHTML = '';
      
      if (library === 'chartjs') {
        chartHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>${title}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 40px; 
            background: #f8fafc;
        }
        .chart-container { 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 { 
            color: #1e293b; 
            margin-bottom: 30px; 
            text-align: center;
        }
        .footer {
            text-align: center;
            color: #64748b;
            font-size: 14px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <h1>${title}</h1>
        <canvas id="myChart"></canvas>
        <div class="footer">
            Generated by Envent Bridge • ${new Date().toLocaleDateString()}
        </div>
    </div>
    <script>
        const ctx = document.getElementById('myChart').getContext('2d');
        new Chart(ctx, ${JSON.stringify(chartConfig)});
    </script>
</body>
</html>`;
      } else if (library === 'echarts') {
        chartHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>${title}</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 40px; 
            background: #f8fafc;
        }
        .chart-container { 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        #chart { 
            width: 100%; 
            height: 400px; 
        }
        .footer {
            text-align: center;
            color: #64748b;
            font-size: 14px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <div id="chart"></div>
        <div class="footer">
            Generated by Envent Bridge • ${new Date().toLocaleDateString()}
        </div>
    </div>
    <script>
        var chart = echarts.init(document.getElementById('chart'));
        chart.setOption(${JSON.stringify(chartConfig)});
        window.addEventListener('resize', function() {
            chart.resize();
        });
    </script>
</body>
</html>`;
      }
      
      console.log('[ExporterAgent] Chart HTML generated successfully');
      return chartHTML;
    } catch (error) {
      logger.error('Failed to create chart', error);
      console.error('[ExporterAgent] Failed to create chart:', error);
      throw error;
    }
  }

  /**
   * Create PDF report
   */
  private async createPDFReport(pdfLib: any, action: ExporterOutput['action'], analysis: ExporterOutput['analysis']): Promise<any> {
    const { PDFDocument, StandardFonts, rgb } = pdfLib;
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

    page.drawText(action.options.title || 'Data Export Report', {
      x: 50,
      y: 750,
      size: 20,
      font: font,
      color: rgb(0, 0, 0),
    });

    page.drawText(`Data items: ${analysis.data_count}`, {
      x: 50,
      y: 700,
      size: 12,
      font: font,
      color: rgb(0, 0, 0),
    });

    return pdfDoc;
  }

  /**
   * Generate interactive table HTML
   */
  private generateInteractiveTable(_gridJS: any, data: any[], _action: ExporterOutput['action']): string {
    // Implementation for generating interactive table HTML
    return `
      <div id="grid-table"></div>
      <script>
        new gridjs.Grid({
          columns: ${JSON.stringify(Object.keys(data[0] || {}))},
          data: ${JSON.stringify(data)},
          pagination: true,
          search: true,
          sort: true,
        }).render(document.getElementById('grid-table'));
      </script>
    `;
  }

  /**
   * Get color scheme for charts
   */
  private getColorScheme(scheme: string): string[] {
    const colorSchemes = {
      default: [
        'rgba(54, 162, 235, 0.6)',
        'rgba(255, 99, 132, 0.6)',
        'rgba(255, 205, 86, 0.6)',
        'rgba(75, 192, 192, 0.6)',
        'rgba(153, 102, 255, 0.6)',
        'rgba(255, 159, 64, 0.6)',
      ],
      blue: [
        'rgba(54, 162, 235, 0.6)',
        'rgba(116, 185, 255, 0.6)',
        'rgba(162, 210, 255, 0.6)',
        'rgba(54, 162, 235, 0.8)',
        'rgba(116, 185, 255, 0.8)',
        'rgba(162, 210, 255, 0.8)',
      ],
      professional: [
        'rgba(71, 85, 105, 0.6)',
        'rgba(100, 116, 139, 0.6)',
        'rgba(148, 163, 184, 0.6)',
        'rgba(203, 213, 225, 0.6)',
        'rgba(71, 85, 105, 0.8)',
        'rgba(100, 116, 139, 0.8)',
      ],
    };

    return colorSchemes[scheme as keyof typeof colorSchemes] || colorSchemes.default;
  }

  /**
   * Take screenshot of current viewport
   */
  async takeCurrentViewportScreenshot(): Promise<ExportResult> {
    try {
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_START, 'Taking current viewport screenshot...');
      
      const result = await chrome.tabs.captureVisibleTab({
        format: 'png',
        quality: 100
      });

      const filename = `screenshot_${Date.now()}.png`;
      
      // Convert data URL to blob and then to downloadable format
      const response = await fetch(result);
      const blob = await response.blob();
      
      // Convert blob to data URL for chrome.downloads
      const arrayBuffer = await blob.arrayBuffer();
      const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      const dataUrl = `data:image/png;base64,${base64}`;

      // Download using chrome.downloads API
      await chrome.downloads.download({
        url: dataUrl,
        filename: filename,
        saveAs: false
      });

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, 'Viewport screenshot captured');

      return {
        success: true,
        filename,
        downloadUrl: dataUrl,
        mimeType: 'image/png',
        size: blob.size
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Screenshot failed';
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);
      return {
        success: false,
        filename: 'screenshot_error',
        error: errorMsg
      };
    }
  }

  async takeFullPageScreenshot(): Promise<ExportResult> {
    try {
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_START, 'Taking full page screenshot...');
      
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (!tab.id) throw new Error('No active tab found');

      // Get page dimensions
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: async function() {
          const body = document.body;
          const html = document.documentElement;
          const pageHeight = Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);
          const viewportHeight = window.innerHeight;
          const scrollSteps = Math.ceil(pageHeight / viewportHeight);
          return { pageHeight, viewportHeight, scrollSteps };
        }
      });

      const { viewportHeight, scrollSteps } = results[0].result as any;
      const screenshots: string[] = [];

      // Capture screenshots by scrolling
      for (let step = 0; step < scrollSteps; step++) {
        const scrollTop = step * viewportHeight;
        
        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          func: (scrollY: number) => window.scrollTo(0, scrollY),
          args: [scrollTop]
        });
        
        await new Promise(resolve => setTimeout(resolve, 300));
        
        const screenshot = await chrome.tabs.captureVisibleTab({
          format: 'png',
          quality: 100
        });
        screenshots.push(screenshot);
      }

      // Combine screenshots
      const combinedImage = await this.combineScreenshots(screenshots, viewportHeight);
      const filename = `fullpage_screenshot_${Date.now()}.png`;
      
      // Convert to blob and create download
      const response = await fetch(combinedImage);
      const blob = await response.blob();
      
      // Convert to data URL for chrome.downloads
      const arrayBuffer = await blob.arrayBuffer();
      const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      const dataUrl = `data:image/png;base64,${base64}`;

      // Download using chrome.downloads API
      await chrome.downloads.download({
        url: dataUrl,
        filename: filename,
        saveAs: false
      });

      // Restore scroll position
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: () => window.scrollTo(0, 0)
      });

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, 'Full page screenshot captured');

      return {
        success: true,
        filename,
        downloadUrl: dataUrl,
        mimeType: 'image/png',
        size: blob.size
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Full page screenshot failed';
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);
      return {
        success: false,
        filename: 'fullpage_screenshot_error',
        error: errorMsg
      };
    }
  }

  /**
   * Start screen recording
   */
  async startScreenRecording(): Promise<ExportResult> {
    try {
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_START, 'Starting screen recording...');

      if (this.isRecording) {
        return {
          success: false,
          filename: 'recording_error',
          error: 'Recording is already in progress'
        };
      }

      // Request screen capture using chrome.desktopCapture
      const streamId = await new Promise<string>((resolve, reject) => {
        chrome.desktopCapture.chooseDesktopMedia(['screen', 'window', 'tab'], (streamId) => {
          if (streamId) {
            resolve(streamId);
          } else {
            reject(new Error('User cancelled screen capture or permission denied'));
          }
        });
      });

      // Get the media stream using the streamId
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: streamId
          }
        } as any,
        video: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: streamId,
            maxWidth: 1920,
            maxHeight: 1080,
            maxFrameRate: 30
          }
        } as any
      });

      // Create MediaRecorder
      this.recordedChunks = [];
      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'video/webm;codecs=vp9'
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        stream.getTracks().forEach(track => track.stop());
      };

      this.mediaRecorder.start();
      this.isRecording = true;

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, 'Screen recording started');

      return {
        success: true,
        filename: 'recording_started',
        mimeType: 'video/webm'
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Screen recording failed to start';
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);
      return {
        success: false,
        filename: 'recording_error',
        error: errorMsg
      };
    }
  }

  /**
   * Stop screen recording and download the video
   */
  async stopScreenRecording(): Promise<ExportResult> {
    try {
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_START, 'Stopping screen recording...');

      if (!this.isRecording || !this.mediaRecorder) {
        return {
          success: false,
          filename: 'recording_error',
          error: 'No recording in progress'
        };
      }

      // Stop recording
      return new Promise<ExportResult>((resolve) => {
        this.mediaRecorder!.onstop = async () => {
          try {
            // Create blob from recorded chunks
            const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
            const filename = `screen_recording_${Date.now()}.webm`;

            // Convert to data URL for download
            const arrayBuffer = await blob.arrayBuffer();
            const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
            const dataUrl = `data:video/webm;base64,${base64}`;

            // Download using chrome.downloads API
            await chrome.downloads.download({
              url: dataUrl,
              filename: filename,
              saveAs: false
            });

            // Reset recording state
            this.isRecording = false;
            this.mediaRecorder = null;
            this.recordedChunks = [];

            this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, 'Screen recording completed and downloaded');

            resolve({
              success: true,
              filename,
              downloadUrl: dataUrl,
              mimeType: 'video/webm',
              size: blob.size
            });
          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Failed to process recording';
            this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);
            resolve({
              success: false,
              filename: 'recording_error',
              error: errorMsg
            });
          }
        };

        this.mediaRecorder!.stop();
      });
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Failed to stop recording';
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);
      return {
        success: false,
        filename: 'recording_error',
        error: errorMsg
      };
    }
  }

  /**
   * Get recording status
   */
  getRecordingStatus(): { isRecording: boolean } {
    return { isRecording: this.isRecording };
  }

  /**
   * Combine multiple screenshots into one image
   */
  private async combineScreenshots(screenshots: string[], viewportHeight: number): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        
        // Load first image to get width
        const firstImg = new Image();
        firstImg.onload = () => {
          canvas.width = firstImg.width;
          canvas.height = screenshots.length * viewportHeight;
          
          let loadedCount = 0;
          const images: HTMLImageElement[] = [];
          
          // Load all images
          screenshots.forEach((screenshot, index) => {
            const img = new Image();
            img.onload = () => {
              images[index] = img;
              loadedCount++;
              
              if (loadedCount === screenshots.length) {
                // Draw all screenshots
                images.forEach((img, i) => {
                  ctx.drawImage(img, 0, i * viewportHeight);
                });
                
                resolve(canvas.toDataURL('image/png'));
              }
            };
            img.onerror = () => reject(new Error(`Failed to load screenshot ${index}`));
            img.src = screenshot;
          });
        };
        firstImg.onerror = () => reject(new Error('Failed to load first screenshot'));
        firstImg.src = screenshots[0];
      } catch (error) {
        reject(error);
      }
    });
  }
}
