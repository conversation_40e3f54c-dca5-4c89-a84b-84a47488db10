import { createLogger } from '../log';
import type { ExtractedData } from './dataExtractionService';

const logger = createLogger('DataExportService');

export interface ExportOptions {
  format: 'csv' | 'json' | 'excel';
  filename?: string;
  includeMetadata?: boolean;
  customHeaders?: Record<string, string>;
}

export interface ExportResult {
  success: boolean;
  downloadUrl?: string;
  filename: string;
  size: number;
  error?: string;
}

export class DataExportService {
  /**
   * Export extracted data to the specified format
   */
  async exportData(data: ExtractedData, options: ExportOptions): Promise<ExportResult> {
    try {
      logger.info('Starting data export', { format: options.format, itemCount: data.data.length });

      const filename = options.filename || this.generateFilename(data, options.format);
      
      let blob: Blob;
      let mimeType: string;

      switch (options.format) {
        case 'csv':
          blob = await this.exportToCSV(data, options);
          mimeType = 'text/csv';
          break;
        case 'json':
          blob = await this.exportToJSON(data, options);
          mimeType = 'application/json';
          break;
        case 'excel':
          blob = await this.exportToExcel(data, options);
          mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }

      // Create download using chrome.downloads API instead of URL.createObjectURL
      // Convert blob to data URL for downloads
      const arrayBuffer = await blob.arrayBuffer();
      const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      const dataUrl = `data:${mimeType};base64,${base64}`;

      // Trigger download using chrome.downloads API
      const downloadId = await chrome.downloads.download({
        url: dataUrl,
        filename: filename,
        saveAs: false
      });

      logger.info('Data export completed', { filename, size: blob.size, downloadId });

      return {
        success: true,
        downloadUrl: dataUrl, // Return the data URL for potential future use or logging
        filename,
        size: blob.size,
      };

    } catch (error) {
      logger.error('Data export failed', error);
      return {
        success: false,
        filename: options.filename || 'export',
        size: 0,
        error: error instanceof Error ? error.message : 'Unknown export error',
      };
    }
  }

  /**
   * Export data to CSV format
   */
  private async exportToCSV(data: ExtractedData, options: ExportOptions): Promise<Blob> {
    try {
      console.log('[DataExportService] Starting CSV export with data:', data.data.slice(0, 3));
      
      if (data.data.length === 0) {
        throw new Error('No data to export');
      }

      // Get all unique field names
      const fieldNames = new Set<string>();
      data.data.forEach(item => {
        Object.keys(item).forEach(key => fieldNames.add(key));
      });

      const headers = Array.from(fieldNames);
      
      // Apply custom headers if provided
      const displayHeaders = headers.map(header => 
        options.customHeaders?.[header] || this.formatHeader(header)
      );

      // Create CSV content
      const csvRows: string[] = [];
      
      // Add headers
      csvRows.push(displayHeaders.map(header => this.escapeCSVField(header)).join(','));

      // Add data rows
      data.data.forEach(item => {
        const row = headers.map(header => {
          const value = item[header];
          return this.escapeCSVField(this.formatValue(value));
        });
        csvRows.push(row.join(','));
      });

      // Add metadata if requested
      if (options.includeMetadata && data.metadata) {
        csvRows.push(''); // Empty row
        csvRows.push('Metadata');
        csvRows.push(`Source URL,${this.escapeCSVField(data.metadata.source_url)}`);
        csvRows.push(`Extraction Date,${this.escapeCSVField(new Date(data.metadata.extraction_timestamp).toISOString())}`);
        csvRows.push(`Total Items,${data.metadata.total_items}`);
        csvRows.push(`Extraction Method,${this.escapeCSVField(data.metadata.extraction_method)}`);
        csvRows.push(`Confidence Score,${data.metadata.confidence_score}`);
      }

      const csvContent = csvRows.join('\n');
      console.log('[DataExportService] CSV file generated successfully, size:', csvContent.length);
      
      return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    } catch (error) {
      logger.error('CSV export failed', error);
      console.error('[DataExportService] CSV export failed:', error);
      throw error;
    }
  }

  /**
   * Export data to JSON format
   */
  private async exportToJSON(data: ExtractedData, options: ExportOptions): Promise<Blob> {
    try {
      console.log('[DataExportService] Starting JSON export with data:', data.data.slice(0, 3));
      
      let exportData: any;

      if (options.includeMetadata) {
        exportData = {
          data: data.data,
          metadata: data.metadata,
          export_info: {
            exported_at: new Date().toISOString(),
            format: 'json',
            total_items: data.data.length,
          },
        };
      } else {
        exportData = data.data;
      }

      const jsonContent = JSON.stringify(exportData, null, 2);
      console.log('[DataExportService] JSON file generated successfully, size:', jsonContent.length);
      
      return new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });

    } catch (error) {
      logger.error('JSON export failed', error);
      console.error('[DataExportService] JSON export failed:', error);
      throw error;
    }
  }

  /**
   * Export data to Excel format using xlsx library
   */
  private async exportToExcel(data: ExtractedData, options: ExportOptions): Promise<Blob> {
    try {
      console.log('[DataExportService] Starting Excel export with data:', data.data.slice(0, 3));
      
      if (data.data.length === 0) {
        throw new Error('No data to export');
      }

      // Import the xlsx library dynamically
      const XLSX = await this.loadXLSXLibrary();
      
      // Create a new workbook
      const wb = XLSX.utils.book_new();

      // Get all unique field names
      const fieldNames = new Set<string>();
      data.data.forEach(item => {
        Object.keys(item).forEach(key => fieldNames.add(key));
      });

      const headers = Array.from(fieldNames);
      
      // Apply custom headers if provided
      const displayHeaders = headers.map(header => 
        options.customHeaders?.[header] || this.formatHeader(header)
      );

      // Create worksheet data
      const wsData: any[][] = [];
      
      // Add headers
      wsData.push(displayHeaders);

      // Add data rows
      data.data.forEach(item => {
        const row = headers.map(header => {
          const value = item[header];
          return this.formatValueForExcel(value);
        });
        wsData.push(row);
      });

      // Create worksheet from data
      const ws = XLSX.utils.aoa_to_sheet(wsData);

      // Set column widths for better display
      const maxWidth = 50;
      const colWidths = headers.map(header => {
        const maxLength = Math.max(
          header.length,
          ...data.data.map(item => String(item[header] || '').length)
        );
        return { wch: Math.min(maxLength + 2, maxWidth) };
      });
      ws['!cols'] = colWidths;

      // Add the main data sheet
      XLSX.utils.book_append_sheet(wb, ws, 'Data');

      // Add metadata sheet if requested
      if (options.includeMetadata && data.metadata) {
        const metadataData = [
          ['Property', 'Value'],
          ['Source URL', data.metadata.source_url],
          ['Extraction Date', new Date(data.metadata.extraction_timestamp).toISOString()],
          ['Total Items', data.metadata.total_items.toString()],
          ['Extraction Method', data.metadata.extraction_method],
          ['Confidence Score', data.metadata.confidence_score.toString()],
        ];

        const metadataWs = XLSX.utils.aoa_to_sheet(metadataData);
        metadataWs['!cols'] = [{ wch: 20 }, { wch: 50 }];
        XLSX.utils.book_append_sheet(wb, metadataWs, 'Metadata');
      }

      // Write the workbook to array buffer
      const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      
      console.log('[DataExportService] Excel file generated successfully, size:', wbout.byteLength);
      
      return new Blob([wbout], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });

    } catch (error) {
      logger.error('Excel export failed', error);
      console.error('[DataExportService] Excel export failed:', error);
      throw error;
    }
  }

  /**
   * Load the xlsx library dynamically
   */
  private async loadXLSXLibrary(): Promise<any> {
    try {
      // In service worker/background script, we need to use importScripts or dynamic import
      // For now, let's create a simple fallback that uses fetch and eval
      const xlsxPath = chrome.runtime.getURL('node_modules/@lib/xlsx.full.min.js');
      
      // Check if XLSX is already loaded
      if ((globalThis as any).XLSX) {
        console.log('[DataExportService] XLSX already loaded');
        return (globalThis as any).XLSX;
      }

      console.log('[DataExportService] Loading XLSX library from:', xlsxPath);
      
      // Fetch the library code and evaluate it
      const response = await fetch(xlsxPath);
      if (!response.ok) {
        throw new Error(`Failed to fetch XLSX library: ${response.status}`);
      }
      
      const xlsxCode = await response.text();
      
      // This function should not be used anymore - XLSX loading is now handled in content script
      throw new Error('Legacy XLSX loading function - use content script injection instead');
      
    } catch (error) {
      logger.error('Failed to load XLSX library', error);
      console.error('[DataExportService] Failed to load XLSX library:', error);
      throw new Error('XLSX library not available');
    }
  }

  /**
   * Format values specifically for Excel export
   */
  private formatValueForExcel(value: any): any {
    if (value === null || value === undefined) {
      return '';
    }
    
    // Keep numbers as numbers for Excel
    if (typeof value === 'number') {
      return value;
    }
    
    // Keep booleans as booleans
    if (typeof value === 'boolean') {
      return value;
    }
    
    // Convert dates to Excel date format
    if (value instanceof Date) {
      return value;
    }
    
    // Try to parse date strings
    if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}/)) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        return date;
      }
    }
    
    // Handle objects and arrays
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  }

  /**
   * Generate filename based on data and format
   */
  private generateFilename(data: ExtractedData, format: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    let domain = 'extracted_data';

    try {
      if (data.metadata.source_url && data.metadata.source_url !== 'AI_GENERATED' && data.metadata.source_url !== 'VALIDATOR_RESULT') {
        domain = new URL(data.metadata.source_url).hostname.replace(/[^a-zA-Z0-9]/g, '_');
      } else if (data.metadata.extraction_method) {
        domain = data.metadata.extraction_method.replace(/[^a-zA-Z0-9]/g, '_');
      }
    } catch (error) {
      logger.error('Failed to parse source URL for filename, using default', {
        sourceUrl: data.metadata.source_url,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    const extension = format === 'excel' ? 'xlsx' : format;
    return `${domain}_${timestamp}.${extension}`;
  }

  /**
   * Format header names for display
   */
  private formatHeader(header: string): string {
    return header
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Format values for export
   */
  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  }

  /**
   * Escape CSV field values
   */
  private escapeCSVField(field: string): string {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  /**
   * Trigger file download
   */
  private async triggerDownload(url: string, filename: string): Promise<void> {
    try {
      // Use Chrome downloads API if available
      if (typeof chrome !== 'undefined' && chrome?.downloads) {
        await new Promise<void>((resolve, reject) => {
          chrome.downloads.download({
            url: url,
            filename: filename,
            saveAs: true,
          }, (downloadId) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve();
            }
          });
        });
      } else {
        // Fallback: In background script, we can't create DOM elements
        // Instead, we'll send a message to content script or use a different approach
        throw new Error('Chrome downloads API not available');
      }
    } catch (error) {
      logger.error('Download trigger failed', error);
      throw error;
    }
  }

  /**
   * Advanced export with enhanced formatting and visualizations
   */
  async exportDataAdvanced(data: ExtractedData, options: any): Promise<ExportResult> {
    try {
      logger.info('Starting advanced data export', { format: options.format });

      const filename = options.filename || this.generateFilename(data, options.format);
      
      switch (options.format) {
        case 'excel':
          return await this.exportToAdvancedExcel(data, options);
        case 'pdf':
          return await this.exportToAdvancedPDF(data, options);
        case 'dashboard':
          return await this.exportToDashboard(data, options);
        default:
          // Fallback to regular export
          return await this.exportData(data, { format: options.format as any, filename, includeMetadata: true });
      }
    } catch (error) {
      logger.error('Advanced export failed', error);
      return {
        success: false,
        filename: options.filename || 'export_failed',
        size: 0,
        error: error instanceof Error ? error.message : 'Unknown export error',
      };
    }
  }

  /**
   * Generate interactive charts from data
   */
  async generateChart(data: any[], options: any): Promise<{ success: boolean; chartHtml?: string; error?: string }> {
    try {
      logger.info('Generating chart', { type: options.chartType, dataLength: data.length });

      if (!data.length) {
        throw new Error('No data available for chart generation');
      }

      const chartConfig = this.buildChartConfig(data, options);
      const chartHtml = this.generateChartHTML(chartConfig, options);

      // Create blob and use chrome.downloads API to download the chart as an HTML file
      const blob = new Blob([chartHtml], { type: 'text/html' });
      const arrayBuffer = await blob.arrayBuffer();
      const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      const dataUrl = `data:text/html;base64,${base64}`;

      await chrome.downloads.download({
        url: dataUrl,
        filename: `chart_${options.title.replace(/\\s+/g, '_')}_${Date.now()}.html`,
        saveAs: false
      });

      return {
        success: true,
        chartHtml,
      };
    } catch (error) {
      logger.error('Chart generation failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Chart generation failed',
      };
    }
  }

  /**
   * Generate professional PDF report
   */
  async generatePdfReport(data: ExtractedData, options: any): Promise<ExportResult> {
    try {
      logger.info('Generating PDF report', { title: options.title });

      const reportHtml = this.generateReportHTML(data, options);
      const filename = `${options.title.replace(/\\s+/g, '_')}_${Date.now()}.html`;

      const blob = new Blob([reportHtml], { type: 'text/html' });
      const arrayBuffer = await blob.arrayBuffer();
      const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      const dataUrl = `data:text/html;base64,${base64}`;

      await chrome.downloads.download({
        url: dataUrl,
        filename,
        saveAs: false
      });

      return {
        success: true,
        downloadUrl: dataUrl,
        filename,
        size: blob.size,
      };
    } catch (error) {
      logger.error('PDF report generation failed', error);
      return {
        success: false,
        filename: 'report_failed',
        size: 0,
        error: error instanceof Error ? error.message : 'PDF report generation failed',
      };
    }
  }

  /**
   * AI-powered export analysis and recommendations
   */
  async analyzeDataForExport(data: ExtractedData, options: any): Promise<{
    success: boolean;
    recommendations?: string[];
    bestExportFormat?: string;
    error?: string;
  }> {
    try {
      logger.info('Analyzing data for export recommendations');

      const analysis = this.performDataAnalysis(data, options);
      
      return {
        success: true,
        recommendations: analysis.recommendations,
        bestExportFormat: analysis.bestFormat,
      };
    } catch (error) {
      logger.error('Data analysis failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Data analysis failed',
      };
    }
  }

  /**
   * Generate interactive dashboard
   */
  async generateDashboard(data: ExtractedData, options: any): Promise<ExportResult> {
    try {
      logger.info('Generating interactive dashboard', { title: options.title });

      const dashboardHtml = this.generateDashboardHTML(data, options);
      const filename = `dashboard_${options.title.replace(/\\s+/g, '_')}_${Date.now()}.html`;

      const blob = new Blob([dashboardHtml], { type: 'text/html' });
      const arrayBuffer = await blob.arrayBuffer();
      const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      const dataUrl = `data:text/html;base64,${base64}`;

      await chrome.downloads.download({
        url: dataUrl,
        filename,
        saveAs: false
      });

      return {
        success: true,
        downloadUrl: dataUrl,
        filename,
        size: blob.size,
      };
    } catch (error) {
      logger.error('Dashboard generation failed', error);
      return {
        success: false,
        filename: 'dashboard_failed',
        size: 0,
        error: error instanceof Error ? error.message : 'Dashboard generation failed',
      };
    }
  }

  // Helper methods for advanced export functionality

  private async exportToAdvancedExcel(data: ExtractedData, options: any): Promise<ExportResult> {
    // Enhanced Excel export with formatting, charts, multiple sheets
    // For now, use the basic Excel export as foundation
    return await this.exportData(data, { format: 'excel', filename: options.filename, includeMetadata: true });
  }

  private async exportToAdvancedPDF(data: ExtractedData, options: any): Promise<ExportResult> {
    // Generate HTML that can be converted to PDF
    const reportHtml = this.generateReportHTML(data, options);
    const filename = options.filename || `report_${Date.now()}.html`;

    const blob = new Blob([reportHtml], { type: 'text/html' });
    const arrayBuffer = await blob.arrayBuffer();
    const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
    const dataUrl = `data:text/html;base64,${base64}`;

    await chrome.downloads.download({
      url: dataUrl,
      filename,
      saveAs: false
    });

    return {
      success: true,
      downloadUrl: dataUrl,
      filename,
      size: blob.size,
    };
  }

  private async exportToDashboard(data: ExtractedData, options: any): Promise<ExportResult> {
    return await this.generateDashboard(data, options);
  }

  private buildChartConfig(data: any[], options: any): any {
    // Build Chart.js configuration based on data and options
    const labels = data.map((item, index) => 
      options.xAxisField ? item[options.xAxisField] : `Item ${index + 1}`
    );
    
    const dataset = {
      label: options.title,
      data: data.map(item => 
        options.yAxisField ? item[options.yAxisField] : Object.values(item)[0]
      ),
      backgroundColor: this.getColorScheme(options.colorScheme),
    };

    return {
      type: options.chartType,
      data: {
        labels,
        datasets: [dataset],
      },
      options: {
        responsive: true,
        plugins: {
          title: {
            display: true,
            text: options.title,
          },
        },
      },
    };
  }

  private generateChartHTML(chartConfig: any, options: any): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>${options.title}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .chart-container { width: 80%; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="myChart"></canvas>
    </div>
    <script>
        const ctx = document.getElementById('myChart').getContext('2d');
        new Chart(ctx, ${JSON.stringify(chartConfig)});
    </script>
</body>
</html>`;
  }

  private generateReportHTML(data: ExtractedData, options: any): string {
    const tableRows = data.data.map(item => {
      const cells = Object.values(item).map(value => `<td>${value}</td>`).join('');
      return `<tr>${cells}</tr>`;
    }).join('');

    const tableHeaders = Object.keys(data.data[0] || {}).map(key => `<th>${key}</th>`).join('');

    return `
<!DOCTYPE html>
<html>
<head>
    <title>${options.title}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #333; border-bottom: 2px solid #333; }
        h2 { color: #666; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .summary { background-color: #f9f9f9; padding: 20px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>${options.title}</h1>
    ${options.subtitle ? `<h2>${options.subtitle}</h2>` : ''}
    
    ${options.includeSummary ? `
    <div class="summary">
        <h3>Executive Summary</h3>
        <p>This report contains ${data.data.length} data items extracted from ${data.metadata?.source_url || 'web source'}.</p>
        <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
    </div>
    ` : ''}
    
    <h3>Data Table</h3>
    <table>
        <thead>
            <tr>${tableHeaders}</tr>
        </thead>
        <tbody>
            ${tableRows}
        </tbody>
    </table>
</body>
</html>`;
  }

  private generateDashboardHTML(data: ExtractedData, options: any): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>${options.title}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .widget { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .widget h3 { margin-top: 0; color: #333; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .search { margin-bottom: 20px; padding: 10px; width: 100%; box-sizing: border-box; }
    </style>
</head>
<body>
    <h1>${options.title}</h1>
    
    ${options.includeSearch ? '<input type="text" class="search" placeholder="Search data..." onkeyup="filterTable(this.value)">' : ''}
    
    <div class="dashboard">
        <div class="widget">
            <h3>Data Overview</h3>
            <p>Total Records: ${data.data.length}</p>
            <p>Source: ${data.metadata?.source_url || 'Web'}</p>
        </div>
        
        <div class="widget">
            <h3>Data Table</h3>
            <table id="dataTable">
                <thead>
                    <tr>${Object.keys(data.data[0] || {}).map(key => `<th>${key}</th>`).join('')}</tr>
                </thead>
                <tbody>
                    ${data.data.map(item => 
                      `<tr>${Object.values(item).map(value => `<td>${value}</td>`).join('')}</tr>`
                    ).join('')}
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        function filterTable(searchTerm) {
            const table = document.getElementById('dataTable');
            const rows = table.getElementsByTagName('tr');
            
            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
            }
        }
    </script>
</body>
</html>`;
  }

  private performDataAnalysis(data: ExtractedData, options: any): {
    recommendations: string[];
    bestFormat: string;
  } {
    const dataLength = data.data.length;
    const fieldCount = Object.keys(data.data[0] || {}).length;
    const recommendations: string[] = [];
    let bestFormat = 'csv';

    // Analyze data characteristics
    if (dataLength > 1000) {
      recommendations.push('Large dataset detected - consider CSV for performance');
      bestFormat = 'csv';
    } else if (fieldCount > 10) {
      recommendations.push('Many fields detected - Excel format recommended for better organization');
      bestFormat = 'excel';
    } else if (options.audience === 'executive') {
      recommendations.push('Executive audience - PDF report recommended');
      bestFormat = 'pdf';
    } else if (options.userGoal?.includes('chart') || options.userGoal?.includes('visual')) {
      recommendations.push('Visualization needed - dashboard format recommended');
      bestFormat = 'dashboard';
    }

    // Add more specific recommendations
    if (dataLength < 100) {
      recommendations.push('Small dataset - consider adding charts for better insights');
    }

    if (options.audience === 'technical') {
      recommendations.push('Technical audience - JSON format may be appropriate');
    }

    return { recommendations, bestFormat };
  }

  private getColorScheme(scheme: string): string[] {
    const schemes: Record<string, string[]> = {
      default: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'],
      blue: ['#1f77b4', '#aec7e8', '#377eb8', '#4daf4a', '#984ea3'],
      green: ['#2ca02c', '#98df8a', '#2f8f2f', '#90ee90', '#006400'],
      red: ['#d62728', '#ff9999', '#b22222', '#ffb6c1', '#8b0000'],
      purple: ['#9467bd', '#c5b0d5', '#8a2be2', '#dda0dd', '#4b0082'],
      orange: ['#ff7f0e', '#ffbb78', '#ff8c00', '#ffd700', '#ff6347'],
    };
    
    return schemes[scheme] || schemes.default;
  }
}
