import 'webextension-polyfill';
import {
  agentModelStore,
  AgentNameEnum,
  firewallStore,
  generalSettingsStore,
  llmProviderStore,
} from '@extension/storage';
import BrowserContext from './browser/context';
import { Executor } from './agent/executor';
import { createLogger } from './log';
import { ExecutionState } from './agent/event/types';
import { createChatModel } from './agent/helper';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { DEFAULT_AGENT_OPTIONS } from './agent/types';
import { SpeechToTextService } from './services/speechToText';



const logger = createLogger('background');

const browserContext = new BrowserContext({});
let currentExecutor: Executor | null = null;
let currentPort: chrome.runtime.Port | null = null;


// Setup side panel behavior
chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true }).catch(error => console.error(error));

// Function to check if script is already injected
async function isScriptInjected(tabId: number): Promise<boolean> {
  try {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => Object.prototype.hasOwnProperty.call(window, 'buildDomTree'),
    });
    return results[0]?.result || false;
  } catch (err) {
    console.error('Failed to check script injection status:', err);
    return false;
  }
}

// // Function to inject the buildDomTree script
async function injectBuildDomTree(tabId: number) {
  try {
    // Check if already injected
    const alreadyInjected = await isScriptInjected(tabId);
    if (alreadyInjected) {
      console.log('Scripts already injected, skipping...');
      return;
    }

    await chrome.scripting.executeScript({
      target: { tabId },
      files: ['buildDomTree.js'],
    });
    console.log('Scripts successfully injected');
  } catch (err) {
    console.error('Failed to inject scripts:', err);
  }
}

chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (tabId && changeInfo.status === 'complete' && tab.url?.startsWith('http')) {
    await injectBuildDomTree(tabId);
  }
});

// Listen for debugger detached event
// if canceled_by_user, remove the tab from the browser context
chrome.debugger.onDetach.addListener(async (source, reason) => {
  console.log('Debugger detached:', source, reason);
  if (reason === 'canceled_by_user') {
    if (source.tabId) {
      currentExecutor?.cancel();
      await browserContext.cleanup();
    }
  }
});

// Cleanup when tab is closed
chrome.tabs.onRemoved.addListener(tabId => {
  browserContext.removeAttachedPage(tabId);
});

logger.info('background loaded');

// Listen for simple messages (e.g., from options page)
chrome.runtime.onMessage.addListener(() => {
  // Handle other message types if needed in the future
  // Return false if response is not sent asynchronously
  // return false;
});

// Setup connection listener for long-lived connections (e.g., side panel)
chrome.runtime.onConnect.addListener(port => {
  if (port.name === 'side-panel-connection') {
    currentPort = port;

    port.onMessage.addListener(async message => {
      try {
        switch (message.type) {
          case 'heartbeat':
            // Acknowledge heartbeat
            port.postMessage({ type: 'heartbeat_ack' });
            break;

          case 'new_task': {
            if (!message.task) return port.postMessage({ type: 'error', error: 'No task provided' });
            if (!message.tabId) return port.postMessage({ type: 'error', error: 'No tab ID provided' });

            logger.info('new_task', message.tabId, message.task);
            currentExecutor = await setupExecutor(message.taskId, message.task, browserContext);
            subscribeToExecutorEvents(currentExecutor);

            const result = await currentExecutor.execute();
            logger.info('new_task execution result', message.tabId, result);
            break;
          }
          case 'follow_up_task': {
            if (!message.task) return port.postMessage({ type: 'error', error: 'No follow up task provided' });
            if (!message.tabId) return port.postMessage({ type: 'error', error: 'No tab ID provided' });

            logger.info('follow_up_task', message.tabId, message.task);

            // If executor exists, add follow-up task
            if (currentExecutor) {
              currentExecutor.addFollowUpTask(message.task);
              // Re-subscribe to events in case the previous subscription was cleaned up
              subscribeToExecutorEvents(currentExecutor);
              const result = await currentExecutor.execute();
              logger.info('follow_up_task execution result', message.tabId, result);
            } else {
              // executor was cleaned up, can not add follow-up task
              logger.info('follow_up_task: executor was cleaned up, can not add follow-up task');
              return port.postMessage({ type: 'error', error: 'Executor was cleaned up, can not add follow-up task' });
            }
            break;
          }

          case 'cancel_task': {
            if (!currentExecutor) return port.postMessage({ type: 'error', error: 'No task to cancel' });
            await currentExecutor.cancel();
            break;
          }

          case 'resume_task': {
            if (!currentExecutor) return port.postMessage({ type: 'error', error: 'No task to resume' });
            await currentExecutor.resume();
            return port.postMessage({ 
              type: 'success', 
              result: { success: true }, 
              message: 'Task resumed successfully' 
            });
          }

          case 'pause_task': {
            if (!currentExecutor) return port.postMessage({ type: 'error', error: 'No task to pause' });
            await currentExecutor.pause();
            return port.postMessage({ 
              type: 'success', 
              result: { success: true }, 
              message: 'Task paused successfully' 
            });
          }

          case 'screenshot': {
            if (!message.tabId) return port.postMessage({ type: 'error', error: 'No tab ID provided' });
            const page = await browserContext.switchTab(message.tabId);
            const screenshot = await page.takeScreenshot();
            logger.info('screenshot', message.tabId, screenshot);
            return port.postMessage({ 
              type: 'success', 
              result: { screenshot, success: true }, 
              message: 'Screenshot captured successfully' 
            });
          }

          case 'state': {
            try {
              const browserState = await browserContext.getState(true);
              const elementsText = browserState.elementTree.clickableElementsToString(
                DEFAULT_AGENT_OPTIONS.includeAttributes,
              );

              logger.info('state', browserState);
              logger.info('interactive elements', elementsText);
              return port.postMessage({ 
                type: 'success', 
                result: { success: true }, 
                message: 'State printed to console' 
              });
            } catch (error) {
              logger.error('Failed to get state:', error);
              return port.postMessage({ type: 'error', error: 'Failed to get state' });
            }
          }

          case 'nohighlight': {
            const page = await browserContext.getCurrentPage();
            await page.removeHighlight();
            return port.postMessage({ 
              type: 'success', 
              result: { success: true }, 
              message: 'Highlight removed successfully' 
            });
          }

          case 'export_data': {
            try {
              if (!message.data || !Array.isArray(message.data)) {
                return port.postMessage({ type: 'error', error: 'No valid data provided for export' });
              }

              logger.info('export_data', message.data.length, message.format);

              // Create ExporterAgent if not exists
              if (!currentExecutor) {
                currentExecutor = await setupExecutor('export_task', 'Export extracted data', browserContext);
                subscribeToExecutorEvents(currentExecutor);
              }

              // Set the extracted data in context
              currentExecutor.getContext().lastExtractionResult = {
                success: true,
                data: message.data,
                metadata: message.metadata || {
                  source_url: 'UI_EXPORT',
                  extraction_timestamp: Date.now(),
                  total_items: message.data.length,
                  extraction_method: 'manual_export',
                  confidence_score: 1.0,
                }
              };

              // Use direct export method instead of AI analysis
              const exporter = currentExecutor.getExporter();
              const result = await exporter.exportDataDirect(
                message.format,
                message.filename ? `${message.filename}.${message.format === 'excel' ? 'xlsx' : message.format}` : undefined
              );
              
              logger.info('export_data execution result', result);

              return port.postMessage({ 
                type: 'success', 
                result: result,
                message: result.success ? 'Export completed successfully' : 'Export failed'
              });
            } catch (error) {
              logger.error('Export data failed:', error);
              return port.postMessage({
                type: 'error',
                error: error instanceof Error ? error.message : 'Export failed',
              });
            }
          }

          case 'generate_chart': {
            try {
              if (!message.data || !Array.isArray(message.data)) {
                return port.postMessage({ type: 'error', error: 'No valid data provided for chart generation' });
              }

              logger.info('generate_chart', message.data.length, message.chartType);

              // Create ExporterAgent if not exists
              if (!currentExecutor) {
                currentExecutor = await setupExecutor('chart_task', 'Generate chart from extracted data', browserContext);
                subscribeToExecutorEvents(currentExecutor);
              }

              // Set the extracted data in context
              currentExecutor.getContext().lastExtractionResult = {
                success: true,
                data: message.data,
                metadata: message.metadata || {
                  source_url: 'UI_CHART',
                  extraction_timestamp: Date.now(),
                  total_items: message.data.length,
                  extraction_method: 'chart_generation',
                  confidence_score: 1.0,
                }
              };

              // Use direct chart generation method
              const exporter = currentExecutor.getExporter();
              const chartResult = await exporter.generateChartDirect(
                message.chartType,
                {
                  title: `${message.chartType} Chart`,
                  filename: `chart_${message.chartType}_${Date.now()}.html`,
                  library: 'chartjs'
                }
              );

              logger.info('generate_chart result', chartResult);

              return port.postMessage({ 
                type: 'success', 
                result: chartResult,
                message: chartResult.success ? 'Chart generated successfully' : 'Chart generation failed'
              });
            } catch (error) {
              logger.error('Chart generation failed:', error);
              return port.postMessage({
                type: 'error',
                error: error instanceof Error ? error.message : 'Chart generation failed',
              });
            }
          }

          case 'take_screenshot': {
            try {
              logger.info('take_screenshot', message.fullPage);

              // Create ExporterAgent if not exists
              if (!currentExecutor) {
                currentExecutor = await setupExecutor('screenshot_task', 'Take screenshot', browserContext);
                subscribeToExecutorEvents(currentExecutor);
              }

              const exporter = currentExecutor.getExporter();
              let screenshotResult;

              if (message.fullPage) {
                screenshotResult = await exporter.takeFullPageScreenshot();
              } else {
                screenshotResult = await exporter.takeCurrentViewportScreenshot();
              }

              logger.info('take_screenshot result', screenshotResult);

              return port.postMessage({ 
                type: 'success', 
                result: screenshotResult,
                message: 'Screenshot captured successfully'
              });
            } catch (error) {
              logger.error('Screenshot failed:', error);
              return port.postMessage({
                type: 'error',
                error: error instanceof Error ? error.message : 'Screenshot failed',
              });
            }
          }

          case 'start_screen_recording': {
            try {
              logger.info('start_screen_recording');
              const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
              if (!tab.id) { throw new Error('No active tab found'); }
              
              // Send message to content script for screen recording
              const result = await chrome.tabs.sendMessage(tab.id, {
                type: 'start_screen_recording'
              });
              
              if (result && result.success) {
                return port.postMessage({ 
                  type: 'success', 
                  result: { success: true }, 
                  message: 'Screen recording started successfully' 
                });
              } else {
                throw new Error(result?.error || 'Screen recording start failed');
              }
            } catch (error) {
              logger.error('Screen recording start failed:', error);
              return port.postMessage({ type: 'error', error: error instanceof Error ? error.message : 'Screen recording start failed' });
            }
          }

          case 'stop_screen_recording': {
            try {
              logger.info('stop_screen_recording');
              const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
              if (!tab.id) { throw new Error('No active tab found'); }
              
              // Send message to content script to stop recording
              const result = await chrome.tabs.sendMessage(tab.id, {
                type: 'stop_screen_recording'
              });
              
              if (result && result.success) {
                return port.postMessage({ 
                  type: 'success', 
                  result: { success: true }, 
                  message: 'Screen recording stopped successfully' 
                });
              } else {
                throw new Error(result?.error || 'Screen recording stop failed');
              }
            } catch (error) {
              logger.error('Screen recording stop failed:', error);
              return port.postMessage({ type: 'error', error: error instanceof Error ? error.message : 'Screen recording stop failed' });
            }
          }

          case 'get_recording_status': {
            try {
              if (!currentExecutor) {
                return port.postMessage({
                  type: 'success',
                  result: { isRecording: false },
                  message: 'No recording in progress'
                });
              }

              const exporter = currentExecutor.getExporter();
              const status = exporter.getRecordingStatus();

              return port.postMessage({
                type: 'success',
                result: status,
                message: 'Recording status retrieved'
              });
            } catch (error) {
              logger.error('Get recording status failed:', error);
              return port.postMessage({
                type: 'error',
                error: error instanceof Error ? error.message : 'Failed to get recording status',
              });
            }
          }

          case 'diagnose_dom_tree': {
            try {
              if (!message.tabId) {
                return port.postMessage({ type: 'error', error: 'No tab ID provided' });
              }

              logger.info('diagnose_dom_tree', message.tabId);

              // Run comprehensive DOM tree diagnostics
              const diagnostics = await browserContext.runDomTreeDiagnostics(message.tabId);
              
              logger.info('DOM tree diagnostics result:', diagnostics);

              return port.postMessage({ 
                type: 'success', 
                result: diagnostics,
                message: 'DOM tree diagnostics completed'
              });
            } catch (error) {
              logger.error('DOM tree diagnostics failed:', error);
              return port.postMessage({
                type: 'error',
                error: error instanceof Error ? error.message : 'DOM tree diagnostics failed',
              });
            }
          }

          case 'speech_to_text': {
            try {
              if (!message.audio) {
                return port.postMessage({
                  type: 'speech_to_text_error',
                  error: 'No audio data provided',
                });
              }

              logger.info('Processing speech-to-text request...');

              // Get all providers for speech-to-text service
              const providers = await llmProviderStore.getAllProviders();

              // Create speech-to-text service with all providers
              const speechToTextService = await SpeechToTextService.create(providers);

              // Extract base64 audio data (remove data URL prefix if present)
              let base64Audio = message.audio;
              if (base64Audio.startsWith('data:')) {
                base64Audio = base64Audio.split(',')[1];
              }

              // Transcribe audio
              const transcribedText = await speechToTextService.transcribeAudio(base64Audio);

              logger.info('Speech-to-text completed successfully');
              return port.postMessage({
                type: 'speech_to_text_result',
                text: transcribedText,
              });
            } catch (error) {
              logger.error('Speech-to-text failed:', error);
              return port.postMessage({
                type: 'speech_to_text_error',
                error: error instanceof Error ? error.message : 'Speech recognition failed',
              });
            }
          }

          case 'replay': {
            if (!message.tabId) return port.postMessage({ type: 'error', error: 'No tab ID provided' });
            if (!message.taskId) return port.postMessage({ type: 'error', error: 'No task ID provided' });
            if (!message.historySessionId)
              return port.postMessage({ type: 'error', error: 'No history session ID provided' });
            logger.info('replay', message.tabId, message.taskId, message.historySessionId);

            try {
              // Switch to the specified tab
              await browserContext.switchTab(message.tabId);
              // Setup executor with the new taskId and a dummy task description
              currentExecutor = await setupExecutor(message.taskId, message.task, browserContext);
              subscribeToExecutorEvents(currentExecutor);

              // Run replayHistory with the history session ID
              const result = await currentExecutor.replayHistory(message.historySessionId);
              logger.debug('replay execution result', message.tabId, result);
            } catch (error) {
              logger.error('Replay failed:', error);
              return port.postMessage({
                type: 'error',
                error: error instanceof Error ? error.message : 'Replay failed',
              });
            }
            break;
          }











          default:
            return port.postMessage({ type: 'error', error: 'Unknown message type' });
        }
      } catch (error) {
        console.error('Error handling port message:', error);
        port.postMessage({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    });

    port.onDisconnect.addListener(() => {
      // this event is also triggered when the side panel is closed, so we need to cancel the task
      console.log('Side panel disconnected');
      currentPort = null;
      currentExecutor?.cancel();
    });
  }
});

async function setupExecutor(taskId: string, task: string, browserContext: BrowserContext) {
  const providers = await llmProviderStore.getAllProviders();
  // if no providers, need to display the options page
  if (Object.keys(providers).length === 0) {
    throw new Error('Please configure API keys in the settings first');
  }
  const agentModels = await agentModelStore.getAllAgentModels();
  // verify if every provider used in the agent models exists in the providers
  for (const agentModel of Object.values(agentModels)) {
    if (!providers[agentModel.provider]) {
      throw new Error(`Provider ${agentModel.provider} not found in the settings`);
    }
  }

  const navigatorModel = agentModels[AgentNameEnum.Navigator];
  if (!navigatorModel) {
    throw new Error('Please choose a model for the navigator in the settings first');
  }
  // Log the provider config being used for the navigator
  const navigatorProviderConfig = providers[navigatorModel.provider];
  const navigatorLLM = createChatModel(navigatorProviderConfig, navigatorModel);

  let plannerLLM: BaseChatModel | null = null;
  const plannerModel = agentModels[AgentNameEnum.Planner];
  if (plannerModel) {
    // Log the provider config being used for the planner
    const plannerProviderConfig = providers[plannerModel.provider];
    plannerLLM = createChatModel(plannerProviderConfig, plannerModel);
  }

  let validatorLLM: BaseChatModel | null = null;
  const validatorModel = agentModels[AgentNameEnum.Validator];
  if (validatorModel) {
    // Log the provider config being used for the validator
    const validatorProviderConfig = providers[validatorModel.provider];
    validatorLLM = createChatModel(validatorProviderConfig, validatorModel);
  }

  let exporterLLM: BaseChatModel | null = null;
  const exporterModel = agentModels[AgentNameEnum.Exporter];
  if (exporterModel) {
    // Log the provider config being used for the exporter
    const exporterProviderConfig = providers[exporterModel.provider];
    exporterLLM = createChatModel(exporterProviderConfig, exporterModel);
  }

  // Apply firewall settings to browser context
  const firewall = await firewallStore.getFirewall();
  if (firewall.enabled) {
    browserContext.updateConfig({
      allowedUrls: firewall.allowList,
      deniedUrls: firewall.denyList,
    });
  } else {
    browserContext.updateConfig({
      allowedUrls: [],
      deniedUrls: [],
    });
  }

  const generalSettings = await generalSettingsStore.getSettings();
  browserContext.updateConfig({
    minimumWaitPageLoadTime: generalSettings.minWaitPageLoad / 1000.0,
    displayHighlights: generalSettings.displayHighlights,
  });

  const executor = new Executor(task, taskId, browserContext, navigatorLLM, {
    plannerLLM: plannerLLM ?? navigatorLLM,
    validatorLLM: validatorLLM ?? navigatorLLM,
    exporterLLM: exporterLLM ?? navigatorLLM,
    agentOptions: {
      maxSteps: generalSettings.maxSteps,
      maxFailures: generalSettings.maxFailures,
      maxActionsPerStep: generalSettings.maxActionsPerStep,
      useVision: generalSettings.useVision,
      useVisionForPlanner: true,
      planningInterval: generalSettings.planningInterval,
    },
    generalSettings: generalSettings,
  });

  return executor;
}

// Update subscribeToExecutorEvents to use port
async function subscribeToExecutorEvents(executor: Executor) {
  // Clear previous event listeners to prevent multiple subscriptions
  executor.clearExecutionEvents();

  // Subscribe to new events
  executor.subscribeExecutionEvents(async event => {
    try {
      if (currentPort) {
        currentPort.postMessage(event);
      }
    } catch (error) {
      logger.error('Failed to send message to side panel:', error);
    }

    if (
      event.state === ExecutionState.TASK_OK ||
      event.state === ExecutionState.TASK_FAIL ||
      event.state === ExecutionState.TASK_CANCEL
    ) {
      await currentExecutor?.cleanup();
    }
  });
}
